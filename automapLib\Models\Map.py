# generated by datamodel-codegen:
#   filename:  map.json
#   timestamp: 2024-09-11T13:27:34+00:00

from __future__ import annotations

from typing import List, Optional, Union

from pydantic import BaseModel


class Element(BaseModel):
    groupid: Optional[str] = None
    hostid: Optional[Union[int, str]] = None
    triggerid: Optional[int] = None
    sysmapid: Optional[int] = None


class Url(BaseModel):
    sysmapelementurlid: str
    selementid: str
    name: str
    url: str


class Tag(BaseModel):
    tag: str
    value: str
    operator: int


class Selement(BaseModel):
    selementid: str
    sysmapid: str
    elementtype: int
    iconid_off: str
    iconid_on: str
    label: str
    label_location: Union[int, str]
    x: int
    y: int
    iconid_disabled: str
    iconid_maintenance: str
    elementsubtype: int
    areatype: int
    width: int
    height: int
    viewtype: int
    use_iconmap: int
    evaltype: int
    elements: List[Element]
    urls: List[Url]
    tags: List[Tag]
    permission: int


class Shape(BaseModel):
    sysmap_shapeid: str
    type: int
    x: int
    y: int
    width: int
    height: int
    text: str
    font: int
    font_size: int
    font_color: str
    text_halign: int
    text_valign: int
    border_type: int
    border_width: int
    border_color: str
    background_color: str
    zindex: int


class Line(BaseModel):
    sysmap_shapeid: str
    zindex: int
    x1: int
    y1: int
    x2: int
    y2: int
    line_type: int
    line_width: int
    line_color: str


class Linktrigger(BaseModel):
    linktriggerid: str
    linkid: str
    triggerid: str
    drawtype: int
    color: str


class Link(BaseModel):
    linkid: str
    sysmapid: str
    selementid1: str
    selementid2: str
    drawtype: int
    color: str
    label: str
    linktriggers: List[Linktrigger]
    permission: int


class User(BaseModel):
    sysmapuserid: str
    userid: str
    permission: int


class UserGroup(BaseModel):
    sysmapusrgrpid: str
    usrgrpid: str
    permission: int


class Map(BaseModel):
    sysmapid: str
    name: str
    width: int
    height: int
    backgroundid: str
    label_type: int
    label_location: int
    highlight: int
    expandproblem: int
    markelements: int
    show_unack: int
    grid_size: int
    grid_show: int
    grid_align: int
    label_format: int
    label_type_host: int
    label_type_hostgroup: int
    label_type_trigger: int
    label_type_map: int
    label_type_image: int
    label_string_host: str
    label_string_hostgroup: str
    label_string_trigger: str
    label_string_map: str
    label_string_image: str
    iconmapid: str
    expand_macros: int
    severity_min: int
    userid: str
    private: int
    show_suppressed: int
    selements: List[Selement]
    shapes: List[Shape]
    lines: List[Line]
    links: List[Link]
    users: List[User]
    userGroups: List[UserGroup]
